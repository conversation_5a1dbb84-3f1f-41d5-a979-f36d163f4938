import React, { useRef, forwardRef, useImperativeHandle } from 'react';
import { Button } from 'antd';

interface ChatInputProps {
  inputMessage: string;
  isLoading: boolean;
  onInputChange: (value: string) => void;
  onSendMessage: () => void;
  onPause: () => void;
}

export interface ChatInputRef {
  focus: () => void;
}

const ChatInput = forwardRef<ChatInputRef, ChatInputProps>(({
  inputMessage,
  isLoading,
  onInputChange,
  onSendMessage,
  onPause,
}, ref) => {
  const inputRef = useRef<HTMLTextAreaElement>(null);

  useImperativeHandle(ref, () => ({
    focus: () => {
      inputRef.current?.focus();
    },
  }));

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      onSendMessage();
    }
  };

  return (
    <div className="message-input-container">
      <textarea
        ref={inputRef}
        className="message-input"
        placeholder="这里输入您想问的问题"
        value={inputMessage}
        onChange={(e) => onInputChange(e.target.value)}
        onKeyDown={handleKeyDown}
      />
      {isLoading && (
        <div className="pause-button" onClick={onPause}>
          <img src="/assets/gx2r7gsj.png" alt="" />
        </div>
      )}
      <Button className="send-button" onClick={onSendMessage}>
        {isLoading || !inputMessage.trim() ? (
          <img src="/assets/h2o0aizq.svg" alt="" />
        ) : (
          <img src="/assets/gm75s6le.svg" alt="" />
        )}
      </Button>
    </div>
  );
});

ChatInput.displayName = 'ChatInput';

export default ChatInput;
