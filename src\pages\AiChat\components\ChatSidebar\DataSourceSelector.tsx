import React, { useState, useEffect } from 'react';
import { Input, Checkbox, Modal, Button, Tree, message, Tabs } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import { DataNode } from 'antd/es/tree';
import { deleteFile } from '@/services/DataLoom/fileController';
import { getAiDataSources } from '@/services/DataLoom/coreDataSourceController';
import { filterTreeData } from './utils';

interface DataSourceItem {
  id: string;
  name: string;
  type: string;
  table?: string;
  datasourceId: string;
}

// 添加树节点类型定义
interface TreeNode extends Omit<DataNode, 'children'> {
  type?: string;
  datasourceId?: string;
  children?: TreeNode[];
  title: string | React.ReactNode;
}

interface DataSourceSelectorProps {
  isVisible: boolean;
  onClose: () => void;
  onConfirm: (sources: DataSourceItem[]) => void;
  selectedSources: DataSourceItem[];
  currentUser?: any;
}

const DataSourceSelector: React.FC<DataSourceSelectorProps> = ({
  isVisible,
  onClose,
  onConfirm,
  selectedSources,
  currentUser,
}) => {
  const [searchText, setSearchText] = useState<string>('');
  const [activeTab, setActiveTab] = useState<'excel' | 'mysql'>('excel');
  const [pendingSources, setPendingSources] = useState<DataSourceItem[]>([]);
  const [excelData, setExcelData] = useState<any[]>([]);
  const [mysqlTreeData, setMysqlTreeData] = useState<any[]>([]);
  const [hoveredExcelKey, setHoveredExcelKey] = useState<string | null>(null);

  // 同步 selectedSources 到 pendingSources
  useEffect(() => {
    if (isVisible) {
      setPendingSources(selectedSources);
    }
  }, [isVisible, selectedSources]);

  // 加载数据源
  const loadDatasources = async () => {
    const res = await getAiDataSources();
    if (res.code === 0 && res.data) {
      const data: any = res.data;
      // 直接设置 excelData
      const excel =
        data.file?.map((item: any) => ({
          title: item.fileName || '',
          key: item.fileSize || '',
          type: 'excel',
          isLeaf: true,
        })) || [];
      setExcelData(excel);

      // 直接设置 mysqlTreeData，并为 Tree 组件处理 checkable 属性
      const mysql =
        data.mysql?.map((item: any) => ({
          title: item.datasourceName || '',
          key: item.datasourceId || '',
          type: 'mysql',
          disableCheckbox: true,
          checkable: false, // 数据库节点不可check
          children: (item.tableNames || []).map((table: string) => ({
            datasourceId: item.datasourceId || '',
            title: table,
            key: `${item.datasourceId || item.id}-${table}`,
            type: 'table',
            checkable: true, // 表节点可check
          })),
        })) || [];
      setMysqlTreeData(mysql);
    }
  };

  // 在组件显示时加载数据源
  useEffect(() => {
    if (isVisible) {
      loadDatasources();
    }
  }, [isVisible]);

  const handleConfirm = () => {
    onConfirm(pendingSources);
  };

  const handleClose = () => {
    setPendingSources([]);
    onClose();
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="selection-container">
      <div className="selection-header">
        <div className="selection-title">选择数据源</div>
      </div>
      <div className="selection-content">
        <div className="search-container">
          <h3>选择数据源</h3>
          <Input.Search
            placeholder="输入名称搜索"
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            className="search-input"
            allowClear
          />
          <Tabs
            activeKey={activeTab}
            onChange={(key) => setActiveTab(key as 'excel' | 'mysql')}
            items={[
              {
                key: 'excel',
                label: '文件',
                children: (
                  <div style={{ maxHeight: '380px', overflowY: 'auto' }}>
                    {excelData
                      .filter((node) => node.title.includes(searchText))
                      .map((node) => (
                        <div
                          key={node.key}
                          className={`data-source-item ${pendingSources.some((source) => source.id === node.key) ? 'selected' : ''}`}
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            cursor: 'pointer',
                            marginBottom: 6,
                            padding: '6px 0',
                            background: pendingSources.some((source) => source.id === node.key) ? '#e6f7ff' : undefined,
                            position: 'relative',
                          }}
                          onMouseEnter={() => setHoveredExcelKey(node.key)}
                          onMouseLeave={() => setHoveredExcelKey(null)}
                          onClick={() => {
                            const newSource = {
                              id: node.key,
                              name: node.title,
                              type: 'excel',
                              datasourceId: '',
                            };
                            setPendingSources((prev) => {
                              const filteredPrev = prev.filter((source) => source.type !== 'mysql');
                              if (filteredPrev.some((source) => source.id === node.key)) {
                                return filteredPrev.filter((source) => source.id !== node.key);
                              } else {
                                return [...filteredPrev, newSource];
                              }
                            });
                          }}
                        >
                          <Checkbox checked={pendingSources.some((source) => source.id === node.key)} style={{ marginRight: 8 }} />
                          <span className="file-name-text" title={node.title}>
                            {node.title}
                          </span>
                          {hoveredExcelKey === node.key && (
                            <span
                              className="file-delete-btn"
                              style={{
                                position: 'absolute',
                                right: 8,
                                top: '50%',
                                transform: 'translateY(-50%)',
                                color: '#ff4d4f',
                                fontWeight: 'bold',
                                fontSize: 16,
                                cursor: 'pointer',
                                zIndex: 2,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                              }}
                              onClick={async (e) => {
                                e.stopPropagation();
                                Modal.confirm({
                                  title: `确认删除文件 ${node.title}？`,
                                  content: '删除后不可恢复，是否继续？',
                                  okText: '删除',
                                  okType: 'danger',
                                  cancelText: '取消',
                                  onOk: async () => {
                                    try {
                                      const res = await deleteFile({ fileName: node.title, userId: currentUser?.id?.toString() });
                                      if (res.code === 0) {
                                        message.success('删除成功');
                                        loadDatasources();
                                        // 从 pendingSources 中删除
                                        setPendingSources((prev) => prev.filter((source) => source.id !== node.key));
                                      } else {
                                        message.error(res.message || '删除失败');
                                      }
                                    } catch (err) {
                                      message.error('删除失败');
                                    }
                                  },
                                });
                              }}
                            >
                              <DeleteOutlined />
                            </span>
                          )}
                        </div>
                      ))}
                  </div>
                ),
              },
              {
                key: 'mysql',
                label: '数据库',
                children: (
                  // 动态高度
                  <div style={{ maxHeight: 'calc(100vh - 580px)', overflowY: 'auto' }}>
                    <Tree
                      treeData={filterTreeData(mysqlTreeData, searchText)}
                      checkable={true}
                      showLine={true}
                      showIcon={true}
                      defaultExpandAll
                      checkedKeys={pendingSources.map((source) => source.id)}
                      onCheck={(checkedKeys, info) => {
                        if (info.checked) {
                          if ((info.node as TreeNode).type === 'table') {
                            const newSource: DataSourceItem = {
                              id: info.node.key.toString(),
                              name: info.node.title?.toString() || '',
                              type: 'mysql',
                              table: info.node.title?.toString() || '',
                              datasourceId: (info.node as TreeNode).datasourceId || '',
                            };
                            setPendingSources((prev) => {
                              // 如果已经选择了 Excel 类型的数据源，先清除它们
                              const filteredPrev = prev.filter((source) => source.type !== 'excel');
                              return [...filteredPrev, newSource];
                            });
                          }
                        } else {
                          setPendingSources((prev) => prev.filter((source) => source.id !== info.node.key.toString()));
                        }
                      }}
                      onSelect={(selectedKeys, info) => {
                        if ((info.node as TreeNode).type === 'table') {
                          const newSource: DataSourceItem = {
                            id: info.node.key.toString(),
                            name: info.node.title?.toString() || '',
                            type: 'mysql',
                            table: info.node.title?.toString() || '',
                            datasourceId: (info.node as TreeNode).datasourceId || '',
                          };
                          setPendingSources((prev) => {
                            // 如果已经选择了 Excel 类型的数据源，先清除它们
                            const filteredPrev = prev.filter((source) => source.type !== 'excel');
                            if (filteredPrev.some((source) => source.id === info.node.key.toString())) {
                              return filteredPrev.filter((source) => source.id !== info.node.key.toString());
                            } else {
                              return [...filteredPrev, newSource];
                            }
                          });
                        }
                      }}
                    />
                  </div>
                ),
              },
            ]}
          />
          <div className="selection-footer">
            <Button type="primary" className="confirm-button" onClick={handleConfirm}>
              确认
            </Button>
            <Button onClick={handleClose}>关闭</Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DataSourceSelector;
