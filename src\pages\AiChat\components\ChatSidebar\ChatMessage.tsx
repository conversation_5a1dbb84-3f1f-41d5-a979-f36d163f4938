import React, { useRef } from 'react';
import { Button, message } from 'antd';
import { CopyOutlined, RollbackOutlined, DeleteOutlined } from '@ant-design/icons';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkBreaks from 'remark-breaks';
import rehypeRaw from 'rehype-raw';
import { copyToClipboard } from './utils';

interface Message {
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  echart_option?: any;
  uploaded_files?: string[];
}

interface ChatMessageProps {
  message: Message;
  index: number;
  processTip?: string;
  onResend: (content: string) => void;
  onDelete: (index: number) => void;
}

const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  index,
  processTip,
  onResend,
  onDelete,
}) => {
  const inputRef = useRef<HTMLTextAreaElement>(null);

  const handleCopy = async (content: string) => {
    const result = await copyToClipboard(content);
    if (result.success) {
      message.success('复制成功');
    } else {
      message.error('复制失败');
    }
  };

  const handleResend = (content: string) => {
    onResend(content);
    inputRef.current?.focus();
  };

  if (!message.content) {
    return null;
  }

  return (
    <div className={`message ${message.role === 'user' ? 'user-message' : 'bot-message'}`}>
      <div className="message-timestamp">
        {message.role === 'assistant' && (
          <div className="message-icon">
            <img src="/assets/image_1752042073186_4ji1wl.svg" alt="" />
          </div>
        )}
        {message.timestamp}
      </div>
      <div className="message-content">
        {message.role === 'assistant' && message.content === '...' ? (
          <div className="tip-indicator">
            <span>{processTip}请稍等...</span>
          </div>
        ) : (
          <div className="message-content-wrapper markdown-body">
            <ReactMarkdown
              remarkPlugins={[remarkGfm, remarkBreaks]}
              rehypePlugins={[rehypeRaw]}
              children={message.content.replace(/\\n/g, '\n')}
            />
            <div className="message-actions">
              <Button
                type="text"
                size="small"
                icon={<CopyOutlined />}
                onClick={() => handleCopy(message.content)}
                title="复制"
              />
              {message.role === 'user' && (
                <Button
                  type="text"
                  size="small"
                  icon={<RollbackOutlined />}
                  onClick={() => handleResend(message.content)}
                  title="重新发送"
                />
              )}
              <Button
                type="text"
                size="small"
                icon={<DeleteOutlined />}
                onClick={() => onDelete(index)}
                title="删除"
              />
            </div>
          </div>
        )}
      </div>
      {message.role === 'assistant' && message.content === '...' && (
        <div className="typing-indicator">
          <span></span>
          <span></span>
          <span></span>
        </div>
      )}
    </div>
  );
};

export default ChatMessage;
