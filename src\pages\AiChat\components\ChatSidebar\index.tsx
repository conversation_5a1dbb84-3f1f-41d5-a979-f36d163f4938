'use client';

import type React from 'react';
import { useState, useRef, useEffect } from 'react';
import { sendToAI } from '@/services/aiService';
import { useLocation } from 'react-router-dom';

import './index.less';
import { Modal, Button } from 'antd';
import { getUserChatDetail } from '@/services/DataLoom/aiController';
import { useModel } from '@umijs/max';
import { Resizable } from 'react-resizable';
import 'react-resizable/css/styles.css';
import {
  MAX_WIDTH,
  MIN_WIDTH,
  DEFAULT_ANALYST_TYPE,
  DATA_TYPE_MAP,
  calculateInitialWidth,
  formatTimestamp,
  parseUploadedFiles,
} from './utils';
import DataSourceSelector from './DataSourceSelector';
import ChatMessageList from './ChatMessageList';
import ChatInput, { ChatInputRef } from './ChatInput';
import AiSuggestions from './AiSuggestions';

interface Message {
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  echart_option?: any;
  uploaded_files?: string[];
}

interface Chunk {
  type: string;
  data?:
    | string
    | {
        echart_option?: any;
        uploaded_files?: string[];
      };
  chatId?: string;
  echart_option?: any;
}

interface ChatSidebarProps {
  onStartConversation?: () => void;
  onClearConversation?: () => void;
  currentAnalyst?: any;
  currentHistoryId?: string;
  onAnalystChange?: (analyst: { name: string; avatar: string; type?: string }) => void;
}

const ChatSidebar = ({
  onStartConversation,
  onClearConversation,
  currentAnalyst,
  currentHistoryId,
  onAnalystChange,
}: ChatSidebarProps): JSX.Element => {
  const [isSelecting, setIsSelecting] = useState<boolean>(false);
  const { selectedSources, addSource, clearSources } = useModel('dataSource');
  const { addMessage, clearHistory } = useModel('chatHistory');
  const [inputMessage, setInputMessage] = useState<string>('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isPaused, setIsPaused] = useState<boolean>(false);
  const [chatId, setChatId] = useState<string>('');
  const inputRef = useRef<ChatInputRef>(null);
  const pauseController = useRef<AbortController | null>(null);
  const isPausedRef = useRef<boolean>(false);
  const [processTip, setProcessTip] = useState<string>('');
  const [dataAnalysis, setDataAnalysis] = useState<{
    echart_option?: any;
    uploaded_files?: string[];
  } | null>(null);
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const [width, setWidth] = useState(calculateInitialWidth());
  const [selectedAnalyst, setSelectedAnalyst] = useState(currentAnalyst);

  const location = useLocation();
  const [aiSuggestions, setAiSuggestions] = useState<(string | { question: string })[]>([]);

  // 监听 currentAnalyst 变化，同步 selectedAnalyst
  useEffect(() => {
    setSelectedAnalyst(currentAnalyst);
  }, [currentAnalyst]);

  // 添加窗口大小变化的监听器
  useEffect(() => {
    const handleResize = () => {
      setWidth(calculateInitialWidth());
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // 同步isPaused状态到ref
  useEffect(() => {
    isPausedRef.current = isPaused;
  }, [isPaused]);

  // 添加获取历史聊天记录的 useEffect
  useEffect(() => {
    const fetchChatHistory = async () => {
      if (currentHistoryId) {
        try {
          const response = await getUserChatDetail({ chatId: currentHistoryId });
          if (response.code === 0 && response.data) {
            // 将历史消息转换为组件需要的格式
            const historyMessages = response.data.map((item: any) => ({
              id: item.id,
              role: item.chatRole === 0 ? 'user' : 'assistant',
              content: item.content,
              timestamp: item.createTime || formatTimestamp(),
              echart_option: item.chartOption
                ? typeof item.chartOption === 'string'
                  ? item.chartOption.trim().startsWith('[')
                    ? JSON.parse(item.chartOption)
                    : []
                  : item.chartOption
                : [],
              uploaded_files: parseUploadedFiles(item.uploadedFiles),
              chartStyle: item.chartStyle || '',
            }));
            setMessages(historyMessages);
            // 清空历史记录
            clearHistory();
            // 只有当消息中包含 echart_option 或 uploaded_files 时才调用 addMessage
            const hasSpecialContent = historyMessages.some(
              (msg: Message) => msg.echart_option || (msg.uploaded_files && msg.uploaded_files.length > 0),
            );
            if (hasSpecialContent) {
              historyMessages.forEach((msg: Message) => addMessage(msg));
            }

            // 如果有 chatId，设置它
            if (response.data[0]?.chatId) {
              setChatId(response.data[0].chatId);
            }
          }
        } catch (error) {
          console.error('获取历史聊天记录失败:', error);
          // message.error('获取历史聊天记录失败');
        }
      }
    };

    fetchChatHistory();
  }, [currentHistoryId]);

  // 处理 dataAnalysis 的变化
  useEffect(() => {
    if (dataAnalysis) {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage && (dataAnalysis.echart_option || dataAnalysis.uploaded_files)) {
        addMessage({
          ...lastMessage,
          echart_option: dataAnalysis.echart_option,
          uploaded_files: dataAnalysis.uploaded_files,
        });
      }
    }
  }, [dataAnalysis]);

  // 添加路由监听
  useEffect(() => {
    // 清空历史记录
    clearHistory();
    // 清空消息列表
    setMessages([]);
    // 清空输入框
    setInputMessage('');
    // 暂停
    setIsPaused(true);
    setIsLoading(false);
    // 重置选中的数据源
    clearSources();
    // 重置选择状态
    setIsSelecting(false);
    setProcessTip('');
    // 重置 chatId
    setChatId('');
    // 重置当前分析师
    setSelectedAnalyst(null as any);
    if (onAnalystChange) {
      onAnalystChange(null as any);
    }
  }, [location.pathname]);

  const handlePause = () => {
    if (isPaused) {
      // 继续
      setIsPaused(false);
      if (pauseController.current) {
        pauseController.current = null;
      }
    } else {
      // 暂停
      setIsPaused(true);
      setIsLoading(false); // 立即停止loading状态
      if (pauseController.current) {
        pauseController.current.abort();
      }
    }
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const params = {
      userId: currentUser?.id?.toString() || '',
      query: inputMessage,
      agentName: selectedAnalyst?.type || DEFAULT_ANALYST_TYPE,
      chatId: chatId,
      dataType: selectedSources.length ? DATA_TYPE_MAP[selectedSources[0].type] || 0 : 0,
      // dataType为0时可以不传
      dataSource: {
        fileName: selectedSources.filter((source) => source.type === 'excel').map((source) => source.name),
        tableSet: selectedSources.filter((source) => source.type === 'mysql').map((source) => source.table),
        dataSourceId: selectedSources.length ? (selectedSources[0].type === 'mysql' ? selectedSources[0].datasourceId : '') : '',
      },
    };

    const userMessage: Message = {
      role: 'user',
      content: inputMessage,
      timestamp: formatTimestamp(),
    };
    setMessages((prev) => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);
    setIsPaused(false);
    // 让输入框获得焦点
    inputRef.current?.focus();

    try {
      let assistantMessage = '';
      const timestamp = formatTimestamp();

      // 先插入一个占位消息
      setMessages((prev) => [...prev, { role: 'assistant', content: '...', timestamp }]);

      pauseController.current = new AbortController();

      await sendToAI(params, (chunk: Chunk) => {
        if (isPausedRef.current) {
          pauseController.current?.abort();
          return;
        }

        switch (chunk.type) {
          case 'process':
            setProcessTip(typeof chunk.data === 'string' ? chunk.data : '');
            break;
          case 'result':
            assistantMessage += chunk.data;
            setMessages((prev) => {
              const newMessages = [...prev];
              const lastMessage = newMessages[newMessages.length - 1];
              if (lastMessage && lastMessage.role === 'assistant') {
                lastMessage.content = assistantMessage;
              }
              return newMessages;
            });

            break;
          case 'error':
            setProcessTip('');
            setIsLoading(false);
            setMessages((prev) => [
              ...prev,
              {
                role: 'assistant',
                content: '抱歉，发生了错误，请稍后重试。',
                timestamp: formatTimestamp(),
              },
            ]);
            break;
          case 'final':
            setProcessTip('');
            setIsLoading(false);
            // 如果是第一条消息，保存返回的 chatId
            if (!chatId && chunk.chatId) {
              setChatId(chunk.chatId);
            }
            // 保存数据分析结果
            function isNonEmptyObject(obj: any): boolean {
              if (obj === null || typeof obj !== 'object') return false;
              if (Array.isArray(obj)) return obj.length > 0;
              return Object.keys(obj).length > 0;
            }

            if (
              typeof chunk.data === 'object' &&
              chunk.data !== null &&
              (isNonEmptyObject(chunk.data.echart_option) || isNonEmptyObject(chunk.data.uploaded_files))
            ) {
              setDataAnalysis(chunk.data);
            }

            break;
        }
      });
    } catch (error: any) {
      if (error.name === 'AbortError') {
        console.log('Request was paused');
      } else {
        console.error('Error sending message:', error);
        setMessages((prev) => [
          ...prev,
          {
            role: 'assistant',
            content: '抱歉，发生了错误，请稍后重试。',
            timestamp: formatTimestamp(),
          },
        ]);
      }
    } finally {
      if (!isPaused) {
        setIsLoading(false);
        pauseController.current = null;
      }
    }
  };

  // 开始新对话
  const handleStartConversation = () => {
    Modal.confirm({
      title: '确认开启新对话',
      content: '开启新对话将清空当前所有消息，是否继续？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        // 清空历史记录
        clearHistory();
        // 清空消息列表
        setMessages([]);
        // 清空输入框
        setInputMessage('');
        // 暂停
        setIsPaused(true);
        setIsLoading(false); // 立即停止loading状态
        // 重置选中的数据源
        clearSources();
        // 重置选择状态
        setIsSelecting(false);
        setProcessTip('');
        // 重置 chatId
        setChatId('');
        // 重置当前分析师
        setSelectedAnalyst(null as any);
        if (onAnalystChange) {
          onAnalystChange(null as any);
        }

        // 调用父组件的回调
        if (onStartConversation) {
          onStartConversation();
        }
      },
    });
  };

  // 清空会话
  const handleClearConversation = () => {
    setMessages([]);
    // 暂停
    setIsPaused(true);
    setIsLoading(false); // 立即停止loading状态
    setProcessTip('');
    // 重置 chatId
    setChatId('');
    if (onClearConversation) {
      onClearConversation();
    }
  };

  // 返回选择界面
  const handleBackToSelection = () => {
    setIsSelecting(true);
  };

  const onResize = (_: any, { size }: { size: { width: number } }) => {
    setWidth(Math.min(Math.max(size.width, MIN_WIDTH), MAX_WIDTH));
  };

  const onResizeStart = () => {
    document.body.classList.add('resizing');
  };

  const onResizeStop = () => {
    document.body.classList.remove('resizing');
  };

  return (
    <Resizable
      width={width}
      height={0}
      onResize={onResize}
      onResizeStart={onResizeStart}
      onResizeStop={onResizeStop}
      resizeHandles={['w']}
      draggableOpts={{ enableUserSelectHack: false }}
    >
      <div className="data-source-container" style={{ width: `${width}px` }}>
        <Button type="primary" className="start-button" onClick={handleStartConversation}>
          <img src="/assets/k9jb5uya.svg" alt="" />
          开启新对话
        </Button>
        <DataSourceSelector
          isVisible={isSelecting}
          onClose={() => {
            setIsSelecting(false);
          }}
          onConfirm={(sources) => {
            // 清空当前选中的数据源
            clearSources();
            // 添加新的数据源
            sources.forEach((source) => {
              addSource(source);
            });
            setIsSelecting(false);
          }}
          selectedSources={selectedSources}
          currentUser={currentUser}
        />

        <div className="chat-container">
          {!isSelecting && (
            <>
              {selectedSources.length > 0 ? (
                <div className="selected-source" onClick={handleBackToSelection} style={{ cursor: 'pointer' }}>
                  <span>{selectedSources.map((source) => source.name).join(', ')}</span>
                  <img
                    className="close-icon"
                    onClick={(e) => {
                      e.stopPropagation();
                      clearSources();
                    }}
                    src="/assets/vwju7hna.svg"
                    alt=""
                  />
                </div>
              ) : (
                <div className="select-source-button" onClick={handleBackToSelection}>
                  <img src="/assets/ohs9y8h7.svg" alt="" />
                  选择数据源
                </div>
              )}
            </>
          )}

          {!isSelecting && (
            <ChatMessageList
              messages={messages}
              processTip={processTip}
              selectedAnalyst={selectedAnalyst}
              onResend={(content) => {
                setInputMessage(content);
                inputRef.current?.focus();
              }}
              onDeleteMessage={(index) => {
                setMessages((prev) => prev.filter((_, i) => i !== index));
              }}
            />
          )}

          <div className="chat-footer">
            <AiSuggestions
              selectedAnalyst={selectedAnalyst}
              chatId={chatId}
              selectedSources={selectedSources}
              currentUser={currentUser}
              aiSuggestions={aiSuggestions}
              onSuggestionClick={(question) => {
                setInputMessage(question);
                inputRef.current?.focus();
              }}
              onSuggestionsUpdate={setAiSuggestions}
            />
            <div style={{ display: 'flex', alignItems: 'center' }}>
              {selectedAnalyst && selectedAnalyst?.type !== DEFAULT_ANALYST_TYPE ? (
                <Button type="primary">
                  <img src="/assets/image_1752041394383_3s40lu.svg" alt="" />
                  {selectedAnalyst?.assistantName || '默认分析师'}
                </Button>
              ) : null}
              <Button className="clear-button" onClick={handleClearConversation}>
                清空会话
              </Button>
            </div>
            <ChatInput
              ref={inputRef}
              inputMessage={inputMessage}
              isLoading={isLoading}
              onInputChange={setInputMessage}
              onSendMessage={handleSendMessage}
              onPause={handlePause}
            />
          </div>
        </div>
      </div>
    </Resizable>
  );
};

export default ChatSidebar;
