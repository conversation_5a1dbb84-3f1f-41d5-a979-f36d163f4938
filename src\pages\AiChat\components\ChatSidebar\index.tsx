'use client';

import type React from 'react';
import { useState, useRef, useEffect } from 'react';
import { sendToAI } from '@/services/aiService';
import ReactMarkdown from 'react-markdown'; // React 渲染 Markdown 的核心库
import remarkGfm from 'remark-gfm'; // 支持 GitHub 风格的 Markdown（如表格、任务列表等）
import remarkBreaks from 'remark-breaks'; // 把换行符\n渲染成 <br>
import rehypeRaw from 'rehype-raw'; // 允许 Markdown 里混用 HTML
import { useLocation } from 'react-router-dom';
import { debounce } from 'lodash';

import './index.less';
import { Input, Checkbox, Collapse, Modal, Button, Tree, message, Tabs, Tooltip } from 'antd';
import { listUserDataSource, getAiDataSources } from '@/services/DataLoom/coreDataSourceController';
import { getUserChatDetail } from '@/services/DataLoom/aiController';
import { deleteFile } from '@/services/DataLoom/fileController';
import { aiSuggest } from '@/services/DataLoom/yibiaopanjiekou';
import { useModel } from '@umijs/max';
import { Resizable } from 'react-resizable';
import 'react-resizable/css/styles.css';
import { CopyOutlined, RollbackOutlined, DeleteOutlined } from '@ant-design/icons';
import { DataNode } from 'antd/es/tree';

// 侧边栏宽度相关常量
const MAX_WIDTH = 800;
const MIN_WIDTH = 300; // 添加最小宽度常量
const INITIAL_WIDTH_PERCENT = 30; // 初始宽度为视窗宽度的30%

// 添加常量定义
const DEFAULT_ANALYST_TYPE = 'guider';
const isGuider = (analyst?: { type?: string }) => analyst?.type === DEFAULT_ANALYST_TYPE;

// 计算初始宽度的函数
const calculateInitialWidth = () => {
  const viewportWidth = window.innerWidth;
  const initialWidth = Math.min(viewportWidth * (INITIAL_WIDTH_PERCENT / 100), MAX_WIDTH);
  return initialWidth;
};

// 数据类型映射
const DATA_TYPE_MAP: Record<string, number> = {
  default: 0,
  excel: 1,
  mysql: 2,
};

interface DataSourceItem {
  id: string;
  name: string;
  type: string;
  table?: string;
  datasourceId: string;
}

interface Message {
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  echart_option?: any;
  uploaded_files?: string[];
}

interface Chunk {
  type: string;
  data?:
    | string
    | {
        echart_option?: any;
        uploaded_files?: string[];
      };
  chatId?: string;
  echart_option?: any;
}

interface ChatSidebarProps {
  onStartConversation?: () => void;
  onClearConversation?: () => void;
  currentAnalyst?: any;
  currentHistoryId?: string;
  onAnalystChange?: (analyst: { name: string; avatar: string; type?: string }) => void;
}

// AI建议问题的类型定义
interface AiSuggestion {
  question: string;
}

// 添加树节点类型定义
interface TreeNode extends Omit<DataNode, 'children'> {
  type?: string;
  datasourceId?: string;
  children?: TreeNode[];
  title: string | React.ReactNode;
}

const ChatSidebar = ({
  onStartConversation,
  onClearConversation,
  currentAnalyst,
  currentHistoryId,
  onAnalystChange,
}: ChatSidebarProps): JSX.Element => {
  const [isSelecting, setIsSelecting] = useState<boolean>(false);
  const { selectedSources, addSource, removeSource, clearSources } = useModel('dataSource');
  const { addMessage, removeMessage, clearHistory, chatHistory } = useModel('chatHistory');
  const [searchText, setSearchText] = useState<string>('');
  const [inputMessage, setInputMessage] = useState<string>('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isPaused, setIsPaused] = useState<boolean>(false);
  const [chatId, setChatId] = useState<string>('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const pauseController = useRef<AbortController | null>(null);
  const isPausedRef = useRef<boolean>(false);
  const [processTip, setProcessTip] = useState<string>('');
  const [dataAnalysis, setDataAnalysis] = useState<{
    echart_option?: any;
    uploaded_files?: string[];
  } | null>(null);
  const [excelData, setExcelData] = useState<any[]>([]);
  const [mysqlTreeData, setMysqlTreeData] = useState<any[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const [width, setWidth] = useState(calculateInitialWidth());
  const [selectedAnalyst, setSelectedAnalyst] = useState(currentAnalyst);
  const [activeTab, setActiveTab] = useState<'excel' | 'mysql'>('excel');
  // 添加临时状态来存储待处理的数据源
  const [pendingSources, setPendingSources] = useState<DataSourceItem[]>([]);
  const location = useLocation();
  const [hoveredExcelKey, setHoveredExcelKey] = useState<string | null>(null);
  const [aiSuggestions, setAiSuggestions] = useState<(string | AiSuggestion)[]>([]);

  // 监听 currentAnalyst 变化，同步 selectedAnalyst
  useEffect(() => {
    setSelectedAnalyst(currentAnalyst);
  }, [currentAnalyst]);

  // 使用 useRef 来保存防抖函数，避免重复创建
  const debouncedFetchAiSuggest = useRef(
    debounce(async (params: any) => {
      const res = await aiSuggest(params);
      const { code, data } = res;
      if (code === 0) {
        setAiSuggestions(data?.data?.questions || []);
      }
    }, 300),
  ).current;

  const parseUploadedFiles = (uploadedFiles: unknown): string[] => {
    if (!uploadedFiles || uploadedFiles === '[""]') return [];

    if (typeof uploadedFiles === 'string') {
      try {
        // 尝试 JSON.parse（适用于 '["a.csv","b.csv"]'）
        const parsed = JSON.parse(uploadedFiles);
        if (Array.isArray(parsed)) {
          return parsed.map((item) => item.trim());
        }
      } catch (err) {
        // 非 JSON 格式（如 '[a.csv,b.csv]'），手动处理
        return uploadedFiles
          .slice(1, -1) // 去除 [ 和 ]
          .split(',')
          .map((item) => item.trim());
      }
    }

    if (Array.isArray(uploadedFiles)) {
      return uploadedFiles.map((item) => String(item).trim());
    }

    return [];
  };

  // 添加同步 selectedSources 到 pendingSources 的 useEffect
  useEffect(() => {
    if (isSelecting) {
      setPendingSources(selectedSources);
    }
  }, [isSelecting, selectedSources]);

  // 添加窗口大小变化的监听器
  useEffect(() => {
    const handleResize = () => {
      setWidth(calculateInitialWidth());
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // 同步isPaused状态到ref
  useEffect(() => {
    isPausedRef.current = isPaused;
  }, [isPaused]);

  // 在组件挂载时加载数据源
  useEffect(() => {
    loadDatasources();
  }, []);
  // 添加获取历史聊天记录的 useEffect
  useEffect(() => {
    const fetchChatHistory = async () => {
      if (currentHistoryId) {
        try {
          const response = await getUserChatDetail({ chatId: currentHistoryId });
          if (response.code === 0 && response.data) {
            // 将历史消息转换为组件需要的格式
            const historyMessages = response.data.map((item: any) => ({
              id: item.id,
              role: item.chatRole === 0 ? 'user' : 'assistant',
              content: item.content,
              timestamp: item.createTime || formatTimestamp(),
              echart_option: item.chartOption
                ? typeof item.chartOption === 'string'
                  ? item.chartOption.trim().startsWith('[')
                    ? JSON.parse(item.chartOption)
                    : []
                  : item.chartOption
                : [],
              uploaded_files: parseUploadedFiles(item.uploadedFiles),
              chartStyle: item.chartStyle || '',
            }));
            setMessages(historyMessages);
            // 清空历史记录
            clearHistory();
            // 只有当消息中包含 echart_option 或 uploaded_files 时才调用 addMessage
            const hasSpecialContent = historyMessages.some(
              (msg: Message) => msg.echart_option || (msg.uploaded_files && msg.uploaded_files.length > 0),
            );
            if (hasSpecialContent) {
              historyMessages.forEach((msg: Message) => addMessage(msg));
            }

            // 如果有 chatId，设置它
            if (response.data[0]?.chatId) {
              setChatId(response.data[0].chatId);
            }

            // 滚动到底部
            scrollToBottom();
          }
        } catch (error) {
          console.error('获取历史聊天记录失败:', error);
          // message.error('获取历史聊天记录失败');
        }
      }
    };

    fetchChatHistory();
  }, [currentHistoryId]);

  // 处理 dataAnalysis 的变化
  useEffect(() => {
    if (dataAnalysis) {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage && (dataAnalysis.echart_option || dataAnalysis.uploaded_files)) {
        addMessage({
          ...lastMessage,
          echart_option: dataAnalysis.echart_option,
          uploaded_files: dataAnalysis.uploaded_files,
        });
      }
    }
  }, [dataAnalysis]);

  // 猜你想要
  useEffect(() => {
    const params = {
      userId: currentUser?.id?.toString() || '',
      agentName: selectedAnalyst?.type || DEFAULT_ANALYST_TYPE,
      chatId: chatId,
      dataType: selectedSources.length ? DATA_TYPE_MAP[selectedSources[0].type] || 0 : 0,
      dataSource: {
        fileName: selectedSources.filter((source) => source.type === 'excel').map((source) => source.name),
        tableSet: selectedSources.filter((source) => source.type === 'mysql').map((source) => source.table),
        dataSourceId: selectedSources.length ? (selectedSources[0].type === 'mysql' ? selectedSources[0].datasourceId : '') : '',
      },
    };

    debouncedFetchAiSuggest(params);

    // 清理函数
    return () => {
      debouncedFetchAiSuggest.cancel();
    };
  }, [chatId, selectedSources, selectedAnalyst?.type, currentUser?.id]);
  // 添加路由监听
  useEffect(() => {
    // 清空历史记录
    clearHistory();
    // 清空消息列表
    setMessages([]);
    // 清空输入框
    setInputMessage('');
    // 暂停
    setIsPaused(true);
    setIsLoading(false);
    // 重置选中的数据源
    clearSources();
    // 重置选择状态
    setIsSelecting(false);
    setProcessTip('');
    // 重置 chatId
    setChatId('');
    // 重置当前分析师
    setSelectedAnalyst(null as any);
    if (onAnalystChange) {
      onAnalystChange(null as any);
    }
  }, [location.pathname]);

  const loadDatasources = async () => {
    const res = await getAiDataSources();
    if (res.code === 0 && res.data) {
      const data: any = res.data;
      // 直接设置 excelData
      const excel =
        data.file?.map((item: any) => ({
          title: item.fileName || '',
          key: item.fileSize || '',
          type: 'excel',
          isLeaf: true,
        })) || [];
      setExcelData(excel);

      // 直接设置 mysqlTreeData，并为 Tree 组件处理 checkable 属性
      const mysql =
        data.mysql?.map((item: any) => ({
          title: item.datasourceName || '',
          key: item.datasourceId || '',
          type: 'mysql',
          disableCheckbox: true,
          checkable: false, // 数据库节点不可check
          children: (item.tableNames || []).map((table: string) => ({
            datasourceId: item.datasourceId || '',
            title: table,
            key: `${item.datasourceId || item.id}-${table}`,
            type: 'table',
            checkable: true, // 表节点可check
          })),
        })) || [];
      setMysqlTreeData(mysql);
    }
  };

  const scrollToBottom = () => {
    setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  };

  const formatTimestamp = () => {
    const now = new Date();
    return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
  };

  const handlePause = () => {
    if (isPaused) {
      // 继续
      setIsPaused(false);
      if (pauseController.current) {
        pauseController.current = null;
      }
    } else {
      // 暂停
      setIsPaused(true);
      setIsLoading(false); // 立即停止loading状态
      if (pauseController.current) {
        pauseController.current.abort();
      }
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(text);
        message.success('复制成功');
      } else {
        // 回退方案：使用传统的复制方法
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        try {
          const success = document.execCommand('copy');
          if (success) {
            message.success('复制成功');
          }
        } catch (err) {
          console.error('复制失败:', err);
        }
        document.body.removeChild(textArea);
      }
    } catch (err) {
      console.error('复制失败:', err);
    }
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const params = {
      userId: currentUser?.id?.toString() || '',
      query: inputMessage,
      agentName: selectedAnalyst?.type || DEFAULT_ANALYST_TYPE,
      chatId: chatId,
      dataType: selectedSources.length ? DATA_TYPE_MAP[selectedSources[0].type] || 0 : 0,
      // dataType为0时可以不传
      dataSource: {
        fileName: selectedSources.filter((source) => source.type === 'excel').map((source) => source.name),
        tableSet: selectedSources.filter((source) => source.type === 'mysql').map((source) => source.table),
        dataSourceId: selectedSources.length ? (selectedSources[0].type === 'mysql' ? selectedSources[0].datasourceId : '') : '',
      },
    };

    const userMessage: Message = {
      role: 'user',
      content: inputMessage,
      timestamp: formatTimestamp(),
    };
    setMessages((prev) => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);
    setIsPaused(false);
    // 让输入框获得焦点
    inputRef.current?.focus();
    // 滚动到底部
    scrollToBottom();

    try {
      let assistantMessage = '';
      const timestamp = formatTimestamp();

      // 先插入一个占位消息
      setMessages((prev) => [...prev, { role: 'assistant', content: '...', timestamp }]);

      pauseController.current = new AbortController();

      await sendToAI(params, (chunk: Chunk) => {
        if (isPausedRef.current) {
          pauseController.current?.abort();
          return;
        }

        switch (chunk.type) {
          case 'process':
            setProcessTip(typeof chunk.data === 'string' ? chunk.data : '');
            break;
          case 'result':
            assistantMessage += chunk.data;
            setMessages((prev) => {
              const newMessages = [...prev];
              const lastMessage = newMessages[newMessages.length - 1];
              if (lastMessage && lastMessage.role === 'assistant') {
                lastMessage.content = assistantMessage;
              }
              return newMessages;
            });
            scrollToBottom();
            break;
          case 'error':
            setProcessTip('');
            setIsLoading(false);
            setMessages((prev) => [
              ...prev,
              {
                role: 'assistant',
                content: '抱歉，发生了错误，请稍后重试。',
                timestamp: formatTimestamp(),
              },
            ]);
            break;
          case 'final':
            setProcessTip('');
            setIsLoading(false);
            // 如果是第一条消息，保存返回的 chatId
            if (!chatId && chunk.chatId) {
              setChatId(chunk.chatId);
            }
            // 保存数据分析结果
            function isNonEmptyObject(obj: any): boolean {
              if (obj === null || typeof obj !== 'object') return false;
              if (Array.isArray(obj)) return obj.length > 0;
              return Object.keys(obj).length > 0;
            }

            if (
              typeof chunk.data === 'object' &&
              chunk.data !== null &&
              (isNonEmptyObject(chunk.data.echart_option) || isNonEmptyObject(chunk.data.uploaded_files))
            ) {
              setDataAnalysis(chunk.data);
            }

            break;
        }
      });
    } catch (error: any) {
      if (error.name === 'AbortError') {
        console.log('Request was paused');
      } else {
        console.error('Error sending message:', error);
        setMessages((prev) => [
          ...prev,
          {
            role: 'assistant',
            content: '抱歉，发生了错误，请稍后重试。',
            timestamp: formatTimestamp(),
          },
        ]);
      }
    } finally {
      if (!isPaused) {
        setIsLoading(false);
        pauseController.current = null;
      }
    }
  };

  // 开始新对话
  const handleStartConversation = () => {
    Modal.confirm({
      title: '确认开启新对话',
      content: '开启新对话将清空当前所有消息，是否继续？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        // 清空历史记录
        clearHistory();
        // 清空消息列表
        setMessages([]);
        // 清空输入框
        setInputMessage('');
        // 暂停
        setIsPaused(true);
        setIsLoading(false); // 立即停止loading状态
        // 重置选中的数据源
        clearSources();
        // 重置选择状态
        setIsSelecting(false);
        setProcessTip('');
        // 重置 chatId
        setChatId('');
        // 重置当前分析师
        setSelectedAnalyst(null as any);
        if (onAnalystChange) {
          onAnalystChange(null as any);
        }

        // 调用父组件的回调
        if (onStartConversation) {
          onStartConversation();
        }
      },
    });
  };

  // 清空会话
  const handleClearConversation = () => {
    setMessages([]);
    // 暂停
    setIsPaused(true);
    setIsLoading(false); // 立即停止loading状态
    setProcessTip('');
    // 重置 chatId
    setChatId('');
    if (onClearConversation) {
      onClearConversation();
    }
  };

  // 返回选择界面
  const handleBackToSelection = () => {
    setIsSelecting(true);
    // 查询数据源
    loadDatasources();
  };

  const onResize = (e: any, { size }: { size: { width: number } }) => {
    setWidth(Math.min(Math.max(size.width, MIN_WIDTH), MAX_WIDTH));
  };

  const onResizeStart = () => {
    document.body.classList.add('resizing');
  };

  const onResizeStop = () => {
    document.body.classList.remove('resizing');
  };

  // 添加过滤树形数据的函数
  const filterTreeData = (data: TreeNode[], searchText: string): TreeNode[] => {
    if (!searchText) return data;

    return data
      .map((node) => {
        const title = typeof node.title === 'string' ? node.title.toLowerCase() : '';
        const searchLower = searchText.toLowerCase();

        // 检查当前节点是否匹配
        const isMatch = title.includes(searchLower);

        // 递归处理子节点
        const children = node.children ? filterTreeData(node.children, searchText) : [];

        // 如果当前节点匹配或者有匹配的子节点，则保留该节点
        if (isMatch || children.length > 0) {
          return {
            ...node,
            children: children.length > 0 ? children : undefined,
          };
        }

        return null;
      })
      .filter((node): node is NonNullable<typeof node> => node !== null);
  };

  return (
    <Resizable
      width={width}
      height={0}
      onResize={onResize}
      onResizeStart={onResizeStart}
      onResizeStop={onResizeStop}
      resizeHandles={['w']}
      draggableOpts={{ enableUserSelectHack: false }}
    >
      <div className="data-source-container" style={{ width: `${width}px` }}>
        <Button type="primary" className="start-button" onClick={handleStartConversation}>
          <img src="/assets/k9jb5uya.svg" alt="" />
          开启新对话
        </Button>
        {isSelecting && (
          <div className="selection-container">
            <div className="selection-header">
              <div className="selection-title">选择数据源</div>
            </div>
            <div className="selection-content">
              <div className="search-container">
                <h3>选择数据源</h3>
                <Input.Search
                  placeholder="输入名称搜索"
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  className="search-input"
                  allowClear
                />
                <Tabs
                  activeKey={activeTab}
                  onChange={(key) => setActiveTab(key as 'excel' | 'mysql')}
                  items={[
                    {
                      key: 'excel',
                      label: '文件',
                      children: (
                        <div style={{ maxHeight: '380px', overflowY: 'auto' }}>
                          {excelData
                            .filter((node) => node.title.includes(searchText))
                            .map((node) => (
                              <div
                                key={node.key}
                                className={`data-source-item ${pendingSources.some((source) => source.id === node.key) ? 'selected' : ''}`}
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  cursor: 'pointer',
                                  marginBottom: 6,
                                  padding: '6px 0',
                                  background: pendingSources.some((source) => source.id === node.key) ? '#e6f7ff' : undefined,
                                  position: 'relative',
                                }}
                                onMouseEnter={() => setHoveredExcelKey(node.key)}
                                onMouseLeave={() => setHoveredExcelKey(null)}
                                onClick={() => {
                                  const newSource = {
                                    id: node.key,
                                    name: node.title,
                                    type: 'excel',
                                    datasourceId: '',
                                  };
                                  setPendingSources((prev) => {
                                    const filteredPrev = prev.filter((source) => source.type !== 'mysql');
                                    if (filteredPrev.some((source) => source.id === node.key)) {
                                      return filteredPrev.filter((source) => source.id !== node.key);
                                    } else {
                                      return [...filteredPrev, newSource];
                                    }
                                  });
                                }}
                              >
                                <Checkbox checked={pendingSources.some((source) => source.id === node.key)} style={{ marginRight: 8 }} />
                                <span className="file-name-text" title={node.title}>
                                  {node.title}
                                </span>
                                {hoveredExcelKey === node.key && (
                                  <span
                                    className="file-delete-btn"
                                    style={{
                                      position: 'absolute',
                                      right: 8,
                                      top: '50%',
                                      transform: 'translateY(-50%)',
                                      color: '#ff4d4f',
                                      fontWeight: 'bold',
                                      fontSize: 16,
                                      cursor: 'pointer',
                                      zIndex: 2,
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                    }}
                                    onClick={async (e) => {
                                      e.stopPropagation();
                                      Modal.confirm({
                                        title: `确认删除文件 ${node.title}？`,
                                        content: '删除后不可恢复，是否继续？',
                                        okText: '删除',
                                        okType: 'danger',
                                        cancelText: '取消',
                                        onOk: async () => {
                                          try {
                                            const res = await deleteFile({ fileName: node.title, userId: currentUser?.id?.toString() });
                                            if (res.code === 0) {
                                              message.success('删除成功');
                                              loadDatasources();
                                              // 从 selectedSources 中删除
                                              removeSource(node.key);
                                            } else {
                                              message.error(res.message || '删除失败');
                                            }
                                          } catch (err) {
                                            message.error('删除失败');
                                          }
                                        },
                                      });
                                    }}
                                  >
                                    <DeleteOutlined />
                                  </span>
                                )}
                              </div>
                            ))}
                        </div>
                      ),
                    },
                    {
                      key: 'mysql',
                      label: '数据库',
                      children: (
                        // 动态高度
                        <div style={{ maxHeight: 'calc(100vh - 580px)', overflowY: 'auto' }}>
                          <Tree
                            treeData={filterTreeData(mysqlTreeData, searchText)}
                            checkable={true}
                            showLine={true}
                            showIcon={true}
                            defaultExpandAll
                            checkedKeys={pendingSources.map((source) => source.id)}
                            onCheck={(checkedKeys, info) => {
                              if (info.checked) {
                                if ((info.node as TreeNode).type === 'table') {
                                  const newSource: DataSourceItem = {
                                    id: info.node.key.toString(),
                                    name: info.node.title?.toString() || '',
                                    type: 'mysql',
                                    table: info.node.title?.toString() || '',
                                    datasourceId: (info.node as TreeNode).datasourceId || '',
                                  };
                                  setPendingSources((prev) => {
                                    // 如果已经选择了 Excel 类型的数据源，先清除它们
                                    const filteredPrev = prev.filter((source) => source.type !== 'excel');
                                    return [...filteredPrev, newSource];
                                  });
                                }
                              } else {
                                setPendingSources((prev) => prev.filter((source) => source.id !== info.node.key.toString()));
                              }
                            }}
                            onSelect={(selectedKeys, info) => {
                              if ((info.node as TreeNode).type === 'table') {
                                const newSource: DataSourceItem = {
                                  id: info.node.key.toString(),
                                  name: info.node.title?.toString() || '',
                                  type: 'mysql',
                                  table: info.node.title?.toString() || '',
                                  datasourceId: (info.node as TreeNode).datasourceId || '',
                                };
                                setPendingSources((prev) => {
                                  // 如果已经选择了 Excel 类型的数据源，先清除它们
                                  const filteredPrev = prev.filter((source) => source.type !== 'excel');
                                  if (filteredPrev.some((source) => source.id === info.node.key.toString())) {
                                    return filteredPrev.filter((source) => source.id !== info.node.key.toString());
                                  } else {
                                    return [...filteredPrev, newSource];
                                  }
                                });
                              }
                            }}
                          />
                        </div>
                      ),
                    },
                  ]}
                />
                <div className="selection-footer">
                  <Button
                    type="primary"
                    className="confirm-button"
                    onClick={() => {
                      // 清空当前选中的数据源
                      clearSources();
                      // 添加新的数据源
                      pendingSources.forEach((source) => {
                        addSource(source);
                      });
                      setIsSelecting(false);
                    }}
                  >
                    确认
                  </Button>
                  <Button
                    onClick={() => {
                      setIsSelecting(false);
                      clearSources();
                      setPendingSources([]);
                    }}
                  >
                    关闭
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="chat-container">
          {!isSelecting && (
            <>
              {selectedSources.length > 0 ? (
                <div className="selected-source" onClick={handleBackToSelection} style={{ cursor: 'pointer' }}>
                  <span>{selectedSources.map((source) => source.name).join(', ')}</span>
                  <img
                    className="close-icon"
                    onClick={(e) => {
                      e.stopPropagation();
                      clearSources();
                    }}
                    src="/assets/vwju7hna.svg"
                    alt=""
                  />
                </div>
              ) : (
                <div className="select-source-button" onClick={handleBackToSelection}>
                  <img src="/assets/ohs9y8h7.svg" alt="" />
                  选择数据源
                </div>
              )}
            </>
          )}

          <div className="chat-content">
            {!isSelecting && messages.length === 0 ? (
              <>
                {selectedAnalyst ? (
                  isGuider(selectedAnalyst) ? (
                    <div key="welcome" className="welcome-message">
                      <div className="welcome-icon">
                        <img src="/assets/image_1752042073186_4ji1wl.svg" alt="" />
                      </div>
                      <div className="welcome-text">hi,我是深度问数,我可以根据你的问题,分析数据生成图表</div>
                    </div>
                  ) : (
                    // 用 selectedAnalyst 替换原有分析师类型映射
                    <h4 key="analyst-type">您的{selectedAnalyst?.assistantName || '默认分析师'}与您对话</h4>
                  )
                ) : null}
              </>
            ) : (
              <>
                {messages.map(
                  (msg, index) =>
                    msg.content && (
                      <div key={index} className={`message ${msg.role === 'user' ? 'user-message' : 'bot-message'}`}>
                        <div className="message-timestamp">
                          {msg.role === 'assistant' && (
                            <div className="message-icon">
                              <img src="/assets/image_1752042073186_4ji1wl.svg" alt="" />
                            </div>
                          )}
                          {msg.timestamp}
                        </div>
                        <div className="message-content">
                          {msg.role === 'assistant' && msg.content === '...' ? (
                            <div className="tip-indicator">
                              <span>{processTip}请稍等...</span>
                            </div>
                          ) : (
                            <div className="message-content-wrapper markdown-body">
                              <ReactMarkdown
                                remarkPlugins={[remarkGfm, remarkBreaks]}
                                rehypePlugins={[rehypeRaw]}
                                children={msg.content.replace(/\\n/g, '\n')}
                              ></ReactMarkdown>
                              <div className="message-actions">
                                <Button
                                  type="text"
                                  size="small"
                                  icon={<CopyOutlined />}
                                  onClick={() => {
                                    copyToClipboard(msg.content);
                                  }}
                                  title="复制"
                                />
                                {msg.role === 'user' && (
                                  <Button
                                    type="text"
                                    size="small"
                                    icon={<RollbackOutlined />}
                                    onClick={() => {
                                      setInputMessage(msg.content);
                                      inputRef.current?.focus();
                                    }}
                                    title="重新发送"
                                  />
                                )}
                                <Button
                                  type="text"
                                  size="small"
                                  icon={<DeleteOutlined />}
                                  onClick={() => {
                                    setMessages((prev) => prev.filter((_, i) => i !== index));
                                  }}
                                  title="删除"
                                />
                              </div>
                            </div>
                          )}
                        </div>
                        {msg.role === 'assistant' && msg.content === '...' && (
                          <div className="typing-indicator">
                            <span></span>
                            <span></span>
                            <span></span>
                          </div>
                        )}
                      </div>
                    ),
                )}
              </>
            )}
            <div ref={messagesEndRef} />
          </div>

          <div className="chat-footer">
            {selectedAnalyst && (
              <Collapse
                className="guess-you-want"
                ghost
                defaultActiveKey={['1']}
                items={[
                  {
                    key: '1',
                    label: '猜你想要',
                    children: (
                      <div className="guess-options">
                        {aiSuggestions.map((item, index) => {
                          // 处理可能是对象或字符串的情况
                          const questionText = typeof item === 'object' && item !== null ? item.question : item;
                          return (
                            <Button
                              key={index}
                              className="question-btn"
                              onClick={() => {
                                setInputMessage(questionText);
                                inputRef.current?.focus();
                              }}
                            >
                              <span title={questionText}>{questionText}</span>
                            </Button>
                          );
                        })}
                      </div>
                    ),
                  },
                ]}
              />
            )}
            <div style={{ display: 'flex', alignItems: 'center' }}>
              {selectedAnalyst && selectedAnalyst?.type !== DEFAULT_ANALYST_TYPE ? (
                <Button type="primary">
                  <img src="/assets/image_1752041394383_3s40lu.svg" alt="" />
                  {selectedAnalyst?.assistantName || '默认分析师'}
                </Button>
              ) : null}
              <Button className="clear-button" onClick={handleClearConversation}>
                清空会话
              </Button>
            </div>
            <div className="message-input-container">
              <textarea
                ref={inputRef}
                className="message-input"
                placeholder="这里输入您想问的问题"
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSendMessage();
                  }
                }}
              />
              {isLoading && (
                <div className="pause-button" onClick={handlePause}>
                  <img src="/assets/gx2r7gsj.png" alt="" />
                </div>
              )}
              <Button className="send-button" onClick={handleSendMessage}>
                {isLoading || !inputMessage.trim() ? <img src="/assets/h2o0aizq.svg" alt="" /> : <img src="/assets/gm75s6le.svg" alt="" />}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </Resizable>
  );
};

export default ChatSidebar;
