import React, { useRef, useEffect } from 'react';
import ChatMessage from './ChatMessage';
import { isGuider } from './utils';

interface Message {
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  echart_option?: any;
  uploaded_files?: string[];
}

interface ChatMessageListProps {
  messages: Message[];
  processTip?: string;
  selectedAnalyst?: any;
  onResend: (content: string) => void;
  onDeleteMessage: (index: number) => void;
}

const ChatMessageList: React.FC<ChatMessageListProps> = ({
  messages,
  processTip,
  selectedAnalyst,
  onResend,
  onDeleteMessage,
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  return (
    <div className="chat-content">
      {messages.length === 0 ? (
        <>
          {selectedAnalyst ? (
            isGuider(selectedAnalyst) ? (
              <div key="welcome" className="welcome-message">
                <div className="welcome-icon">
                  <img src="/assets/image_1752042073186_4ji1wl.svg" alt="" />
                </div>
                <div className="welcome-text">hi,我是深度问数,我可以根据你的问题,分析数据生成图表</div>
              </div>
            ) : (
              // 用 selectedAnalyst 替换原有分析师类型映射
              <h4 key="analyst-type">您的{selectedAnalyst?.assistantName || '默认分析师'}与您对话</h4>
            )
          ) : null}
        </>
      ) : (
        <>
          {messages.map((msg, index) => (
            <ChatMessage
              key={index}
              message={msg}
              index={index}
              processTip={processTip}
              onResend={onResend}
              onDelete={onDeleteMessage}
            />
          ))}
        </>
      )}
      <div ref={messagesEndRef} />
    </div>
  );
};

export default ChatMessageList;
